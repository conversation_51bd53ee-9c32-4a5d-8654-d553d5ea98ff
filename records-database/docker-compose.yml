version: '3.8'

services:
  records-db:
    image: mysql:8.0
    container_name: records-db
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: records_root_password
      MYSQL_DATABASE: vdb_records
      MYSQL_USER: records_user
      MYSQL_PASSWORD: records_password
    ports:
      - "3307:3306"
    volumes:
      - /vdb_records:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql:ro
      - ./backup:/backup
    networks:
      - shared-network
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "records_user", "-precords_password"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

networks:
  shared-network:
    name: llm-system-network
    external: true

volumes:
  records_data:
    driver: local
