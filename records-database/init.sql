-- 聊天记录数据库初始化脚本
-- 此脚本用于初始化存储聊天记录的数据库

-- 设置字符集
SET NAMES utf8mb4;
SET CHARACTER SET utf8mb4;

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS vdb_records 
    CHARACTER SET utf8mb4 
    COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE vdb_records;

-- 创建应用用户（如果不存在）
CREATE USER IF NOT EXISTS 'records_user'@'%' IDENTIFIED BY 'records_password';

-- 授予权限
GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, DROP, INDEX, ALTER ON vdb_records.* TO 'records_user'@'%';

-- 刷新权限
FLUSH PRIVILEGES;

-- 设置时区
SET time_zone = '+00:00';

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    api_key VARCHAR(255) NOT NULL UNIQUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    permission INT DEFAULT 1 NOT NULL,
    mathjax BOOLEAN DEFAULT FALSE,
    current_model_id INT,
    current_temperature DECIMAL(3,2) DEFAULT 0.70,
    current_conversation_id INT,
    total_deposited DECIMAL(10,4) DEFAULT 0.0000,
    total_spent DECIMAL(10,4) DEFAULT 0.0000,
    current_balance DECIMAL(10,4) DEFAULT 0.0000,
    total_prompt_tokens INT DEFAULT 0,
    total_completion_tokens INT DEFAULT 0,
    INDEX idx_user_api_key (api_key),
    INDEX idx_user_active (is_active),
    INDEX idx_user_permission (permission),
    INDEX idx_user_created (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 对话表
CREATE TABLE IF NOT EXISTS conversations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    latest_revised_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    user_id INT NOT NULL,
    title VARCHAR(255),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_conversation_user (user_id),
    INDEX idx_conversation_created (created_at),
    INDEX idx_conversation_revised (latest_revised_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 消息表
CREATE TABLE IF NOT EXISTS messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    conversation_id INT NOT NULL,
    role VARCHAR(20),
    content TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    model_id INT NOT NULL,
    temperature DECIMAL(3,2),
    max_tokens INT,
    prompt_tokens INT,
    completion_tokens INT,
    prompt_cost DECIMAL(10,6) DEFAULT 0.000000,
    completion_cost DECIMAL(10,6) DEFAULT 0.000000,
    total_cost DECIMAL(10,6) DEFAULT 0.000000,
    is_error BOOLEAN DEFAULT FALSE,
    error_info TEXT,
    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE,
    INDEX idx_message_conversation (conversation_id),
    INDEX idx_message_model (model_id),
    INDEX idx_message_role (role),
    INDEX idx_message_created (created_at),
    INDEX idx_message_error (is_error)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 添加外键约束（如果不存在）
ALTER TABLE users ADD CONSTRAINT fk_user_conversation 
    FOREIGN KEY (current_conversation_id) REFERENCES conversations(id) ON DELETE SET NULL;

-- 创建系统信息表（用于版本管理）
CREATE TABLE IF NOT EXISTS system_info (
    id INT AUTO_INCREMENT PRIMARY KEY,
    version VARCHAR(20) NOT NULL,
    initialized_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    notes TEXT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入初始版本信息
INSERT IGNORE INTO system_info (version, notes) VALUES 
('1.0.0', 'Records database setup for independent Docker service');

-- 优化设置
SET GLOBAL innodb_buffer_pool_size = 64M;
SET GLOBAL max_connections = 100;
