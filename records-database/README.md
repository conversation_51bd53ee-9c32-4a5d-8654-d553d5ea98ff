# 聊天记录数据库服务

这是一个独立的MySQL数据库服务，专门用于存储用户聊天记录和相关信息。

## 功能

- 存储用户信息和权限
- 存储对话记录
- 存储消息内容和统计
- 管理用户余额和token使用情况

## 数据库信息

- **数据库名**: vdb_records
- **端口**: 3307 (映射到容器内的3306)
- **用户**: records_user
- **密码**: records_password
- **数据盘**: /vdb_records

## 包含的表

- `users` - 用户信息、权限、余额等
- `conversations` - 对话记录
- `messages` - 消息内容、token统计、费用等
- `system_info` - 系统版本信息

## 启动服务

```bash
# 创建数据目录
sudo mkdir -p /vdb_records
sudo chown -R 999:999 /vdb_records

# 启动数据库服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

## 连接数据库

```bash
# 使用Docker连接
docker-compose exec records-db mysql -u records_user -p

# 使用外部客户端连接
mysql -h localhost -P 3307 -u records_user -p vdb_records
```

## 停止服务

```bash
docker-compose down
```

## 备份数据

```bash
# 创建备份目录
mkdir -p backup

# 备份数据库
docker-compose exec records-db mysqldump -u records_user -p vdb_records > backup/records_backup_$(date +%Y%m%d_%H%M%S).sql
```

## 注意事项

- 此数据库使用端口3307以避免与模型数据库(3306)冲突
- 数据存储在/vdb_records目录，请确保有足够的磁盘空间
- 建议定期备份重要的聊天记录数据
