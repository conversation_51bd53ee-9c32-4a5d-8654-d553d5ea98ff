-- 大语言模型数据库初始化脚本
-- 此脚本用于初始化存储大语言模型信息的数据库

-- 设置字符集
SET NAMES utf8mb4;
SET CHARACTER SET utf8mb4;

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS vdb_models 
    CHARACTER SET utf8mb4 
    COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE vdb_models;

-- 创建应用用户（如果不存在）
CREATE USER IF NOT EXISTS 'models_user'@'%' IDENTIFIED BY 'models_password';

-- 授予权限
GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, DROP, INDEX, ALTER ON vdb_models.* TO 'models_user'@'%';

-- 刷新权限
FLUSH PRIVILEGES;

-- 设置时区
SET time_zone = '+00:00';

-- 平台表
CREATE TABLE IF NOT EXISTS platforms (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    base_url VARCHAR(255) NOT NULL,
    api_key VARCHAR(255) NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_platform_name (name),
    INDEX idx_platform_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- AI模型表
CREATE TABLE IF NOT EXISTS ai_models (
    id INT AUTO_INCREMENT PRIMARY KEY,
    display_name VARCHAR(50) NOT NULL UNIQUE,
    internal_name VARCHAR(100) NOT NULL,
    api_endpoint VARCHAR(255),
    platform_id INT NOT NULL,
    input_token_price DECIMAL(10,6) DEFAULT 0.000000,
    output_token_price DECIMAL(10,6) DEFAULT 0.000000,
    input_picture_price DECIMAL(10,6) DEFAULT 0.000000,
    is_visible_model BOOLEAN DEFAULT FALSE,
    free BOOLEAN DEFAULT FALSE,
    high_price BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (platform_id) REFERENCES platforms(id) ON DELETE CASCADE,
    INDEX idx_model_display_name (display_name),
    INDEX idx_model_platform (platform_id),
    INDEX idx_model_visible (is_visible_model),
    INDEX idx_model_free (free),
    INDEX idx_model_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 应用表
CREATE TABLE IF NOT EXISTS applications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    api_key VARCHAR(255) UNIQUE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_app_name (name),
    INDEX idx_app_api_key (api_key),
    INDEX idx_app_active (is_active),
    INDEX idx_app_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 应用模型关联表
CREATE TABLE IF NOT EXISTS app_models (
    id INT AUTO_INCREMENT PRIMARY KEY,
    application_id INT NOT NULL,
    model_id INT NOT NULL,
    is_default BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE,
    FOREIGN KEY (model_id) REFERENCES ai_models(id) ON DELETE CASCADE,
    UNIQUE KEY uix_app_model (application_id, model_id),
    INDEX idx_app_model_app (application_id),
    INDEX idx_app_model_model (model_id),
    INDEX idx_app_model_default (is_default),
    INDEX idx_app_model_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入示例平台数据
INSERT IGNORE INTO platforms (name, base_url, api_key) VALUES
('spark', 'https://spark-api-open.xf-yun.com/v1', 'fb4d51d6a4e8802f6c0febf24ac75d58:YmNhZWY2YmI0MDU0ZTU2YjRjMTdhMGU1'),
('OpenRouter', 'https://openrouter.ai/api/v1', '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'),
('deepbricks', 'https://api.deepbricks.ai/v1/', 'sk-pmLx4UQDPWYhQYJqc8MQgbQ6pkmkRoQ6YxTumIy5nQWjSnIR');

-- 创建系统信息表（用于版本管理）
CREATE TABLE IF NOT EXISTS system_info (
    id INT AUTO_INCREMENT PRIMARY KEY,
    version VARCHAR(20) NOT NULL,
    initialized_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    notes TEXT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入初始版本信息
INSERT IGNORE INTO system_info (version, notes) VALUES 
('1.0.0', 'Models database setup for independent Docker service');

-- 优化设置
SET GLOBAL innodb_buffer_pool_size = 64M;
SET GLOBAL max_connections = 100;
