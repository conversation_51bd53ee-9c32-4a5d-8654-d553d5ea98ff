version: '3.8'

services:
  models-db:
    image: mysql:8.0
    container_name: models-db
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: models_root_password
      MYSQL_DATABASE: vdb_models
      MYSQL_USER: models_user
      MYSQL_PASSWORD: models_password
    ports:
      - "3306:3306"
    volumes:
      - /vdb_models:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql:ro
      - ./backup:/backup
    networks:
      - shared-network
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "models_user", "-pmodels_password"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

networks:
  shared-network:
    name: llm-system-network
    driver: bridge

volumes:
  models_data:
    driver: local
