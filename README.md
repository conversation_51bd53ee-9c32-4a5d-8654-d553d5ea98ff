# 大语言模型管理系统 - 独立服务架构

本目录包含4个完全独立的Docker服务，每个服务都包含完整的应用代码和配置，可以单独部署和管理。

## 目录结构

```
services/
├── models-database/          # 大语言模型数据库服务
│   ├── docker-compose.yml
│   ├── init.sql
│   ├── backup/
│   └── README.md
├── records-database/         # 聊天记录数据库服务
│   ├── docker-compose.yml
│   ├── init.sql
│   ├── backup/
│   └── README.md
├── model-manager/           # 模型管理服务
│   ├── docker-compose.yml
│   ├── Dockerfile
│   ├── .env
│   └── README.md
├── api-service/            # API调用服务
│   ├── docker-compose.yml
│   ├── Dockerfile
│   ├── .env
│   └── README.md
├── start-all-services.sh   # 启动所有服务
├── stop-all-services.sh    # 停止所有服务
├── check-services.sh       # 检查服务状态
└── README.md              # 本文档
```

## 4个独立服务

### 1. 模型数据库服务 (models-database)
- **功能**: 存储AI平台、模型配置、应用信息
- **端口**: 3306
- **数据盘**: /vdb_models
- **独立启动**: `cd models-database && docker-compose up -d`

### 2. 聊天记录数据库服务 (records-database)
- **功能**: 存储用户、对话、消息记录
- **端口**: 3307
- **数据盘**: /vdb_records
- **独立启动**: `cd records-database && docker-compose up -d`

### 3. 模型管理服务 (model-manager)
- **功能**: 设置和管理大语言模型
- **端口**: 5001
- **依赖**: models-database
- **独立启动**: `cd model-manager && docker-compose up -d`

### 4. API调用服务 (api-service)
- **功能**: 获取和调用大语言模型
- **端口**: 5002
- **依赖**: records-database, models-database
- **独立启动**: `cd api-service && docker-compose up -d`

## 快速启动

### 启动所有服务
```bash
# 在services目录下执行
./start-all-services.sh
```

### 单独启动服务
```bash
# 启动模型数据库
cd models-database && docker-compose up -d

# 启动聊天记录数据库
cd records-database && docker-compose up -d

# 启动模型管理服务
cd model-manager && docker-compose up -d

# 启动API调用服务
cd api-service && docker-compose up -d
```

## 服务管理

### 检查服务状态
```bash
./check-services.sh
```

### 停止所有服务
```bash
./stop-all-services.sh
```

### 查看服务日志
```bash
# 查看特定服务日志
cd <service-directory>
docker-compose logs -f

# 例如查看API服务日志
cd api-service
docker-compose logs -f
```

## 服务访问

- **模型数据库**: localhost:3306
- **聊天记录数据库**: localhost:3307
- **模型管理界面**: http://localhost:5001
- **API调用界面**: http://localhost:5002

## 数据持久化

- 模型数据库数据: `/vdb_models`
- 聊天记录数据库数据: `/vdb_records`
- 应用日志和上传文件: Docker命名卷

## 服务依赖关系

```
api-service
├── records-database (主要数据库)
├── models-database (只读访问)
└── model-manager (可选)

model-manager
└── models-database

records-database (独立)

models-database (独立)
```

## 启动顺序

1. models-database (模型数据库)
2. records-database (聊天记录数据库)
3. model-manager (模型管理服务)
4. api-service (API调用服务)

## 配置说明

每个服务都有独立的配置文件：
- 数据库服务: `docker-compose.yml` 中的环境变量
- 应用服务: `.env` 文件

## 网络配置

每个服务使用独立的Docker网络，通过 `external_links` 连接其他服务。

## 注意事项

1. **数据目录**: 确保 `/vdb_models` 和 `/vdb_records` 有足够空间
2. **启动顺序**: 按依赖关系启动服务
3. **端口冲突**: 确保端口3306、3307、5001、5002未被占用
4. **安全配置**: 生产环境请修改默认密码和密钥
5. **健康检查**: 使用健康检查端点监控服务状态

## 故障排除

### 服务无法启动
1. 检查端口是否被占用
2. 检查数据目录权限
3. 查看服务日志: `docker-compose logs`

### 数据库连接失败
1. 确保数据库服务已启动
2. 检查网络连接
3. 验证数据库用户名密码

### 服务间通信失败
1. 检查 `external_links` 配置
2. 确保依赖服务已启动
3. 验证服务名称解析

## 扩展和定制

每个服务都可以独立扩展：
- 修改资源限制
- 添加环境变量
- 自定义网络配置
- 添加额外的卷挂载