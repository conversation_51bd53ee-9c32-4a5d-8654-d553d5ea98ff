{% extends "base.html" %}

{% block title %}添加模型 - 大语言模型管理系统{% endblock %}

{% block header %}添加模型{% endblock %}

{% block content %}
<div class="card">
    <div class="card-body">
        <form method="post" action="{{ url_for('main.add_model') }}">
            {{ form.hidden_tag() }}

            <div class="mb-3">
                {{ form.display_name.label(class="form-label") }}
                {{ form.display_name(class="form-control") }}
                {% if form.display_name.errors %}
                    <div class="text-danger">
                        {% for error in form.display_name.errors %}
                            <small>{{ error }}</small><br>
                        {% endfor %}
                    </div>
                {% endif %}
                <div class="form-text">模型的显示名称，如 GPT-4、Claude 等</div>
            </div>

            <div class="mb-3">
                {{ form.internal_name.label(class="form-label") }}
                {{ form.internal_name(class="form-control") }}
                {% if form.internal_name.errors %}
                    <div class="text-danger">
                        {% for error in form.internal_name.errors %}
                            <small>{{ error }}</small><br>
                        {% endfor %}
                    </div>
                {% endif %}
                <div class="form-text">模型的内部标识，如 gpt-4、claude-2 等</div>
            </div>

            <div class="mb-3">
                {{ form.api_endpoint.label(class="form-label") }}
                {{ form.api_endpoint(class="form-control") }}
                {% if form.api_endpoint.errors %}
                    <div class="text-danger">
                        {% for error in form.api_endpoint.errors %}
                            <small>{{ error }}</small><br>
                        {% endfor %}
                    </div>
                {% endif %}
                <div class="form-text">模型的API端点路径（可选）</div>
            </div>

            <div class="mb-3">
                {{ form.platform_id.label(class="form-label") }}
                {{ form.platform_id(class="form-select") }}
                {% if form.platform_id.errors %}
                    <div class="text-danger">
                        {% for error in form.platform_id.errors %}
                            <small>{{ error }}</small><br>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>

            <div class="mb-3">
                <label for="input_token_price" class="form-label">输入Token价格</label>
                <div class="input-group">
                    <span class="input-group-text">$</span>
                    <input type="number" class="form-control" id="input_token_price" name="input_token_price" 
                           step="0.000001" min="0" value="0.000000" required>
                    <span class="input-group-text">/ 1000 tokens</span>
                </div>
                <div class="form-text">每1000个输入token的价格（美元）</div>
            </div>

            <div class="mb-3">
                <label for="output_token_price" class="form-label">输出Token价格</label>
                <div class="input-group">
                    <span class="input-group-text">$</span>
                    <input type="number" class="form-control" id="output_token_price" name="output_token_price" 
                           step="0.000001" min="0" value="0.000000" required>
                    <span class="input-group-text">/ 1000 tokens</span>
                </div>
                <div class="form-text">每1000个输出token的价格（美元）</div>
            </div>

            <div class="mb-3">
                <label for="input_picture_price" class="form-label">输入图片价格</label>
                <div class="input-group">
                    <span class="input-group-text">$</span>
                    <input type="number" class="form-control" id="input_picture_price" name="input_picture_price" 
                           step="0.000001" min="0" value="0.000000" required>
                    <span class="input-group-text">/ 1000 tokens</span>
                </div>
                <div class="form-text">每1000个图片token的价格（美元）</div>
            </div>

            <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="is_visible_model" name="is_visible_model">
                <label class="form-check-label" for="is_visible_model">可见模型</label>
                <div class="form-text">是否在用户界面中显示此模型</div>
            </div>

            <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="free" name="free">
                <label class="form-check-label" for="free">免费模型</label>
                <div class="form-text">是否为免费模型（不计费）</div>
            </div>

            <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="high_price" name="high_price">
                <label class="form-check-label" for="high_price">高价模型</label>
                <div class="form-text">是否为高价模型（需要特殊权限）</div>
            </div>

            <div class="d-flex justify-content-between">
                <a href="{{ url_for('main.list_models') }}" class="btn btn-secondary">返回</a>
                <button type="submit" class="btn btn-primary">保存</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
