{% extends "base.html" %}

{% block title %}编辑应用模型关联 - 大语言模型管理系统{% endblock %}

{% block header %}编辑应用模型关联{% endblock %}

{% block content %}
<div class="card">
    <div class="card-body">
        <form method="post" action="{{ url_for('main.edit_app_model', id=app_model.id) }}">
            <div class="mb-3">
                <label for="application_id" class="form-label">应用</label>
                <select class="form-select" id="application_id" name="application_id" required>
                    <option value="">-- 选择应用 --</option>
                    {% for app in applications %}
                    <option value="{{ app.id }}" {% if app.id == app_model.application_id %}selected{% endif %}>
                        {{ app.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>

            <div class="mb-3">
                <label for="model_id" class="form-label">模型</label>
                <select class="form-select" id="model_id" name="model_id" required>
                    <option value="">-- 选择模型 --</option>
                    {% for model in models %}
                    <option value="{{ model.id }}" {% if model.id == app_model.model_id %}selected{% endif %}>
                        {{ model.display_name }} ({{ model.platform.name }})
                    </option>
                    {% endfor %}
                </select>
            </div>

            <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="is_default" name="is_default"
                       {% if app_model.is_default %}checked{% endif %}>
                <label class="form-check-label" for="is_default">设为默认模型</label>
                <div class="form-text">如果选中，此模型将成为该应用的默认模型（每个应用只能有一个默认模型）</div>
            </div>

            <div class="d-flex justify-content-between">
                <a href="{{ url_for('main.list_app_models') }}" class="btn btn-secondary">返回</a>
                <button type="submit" class="btn btn-primary">更新</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
