{% extends "base.html" %}

{% block title %}编辑应用 - 大语言模型管理系统{% endblock %}

{% block header %}编辑应用{% endblock %}

{% block content %}
<div class="card">
    <div class="card-body">
        <form method="post" action="{{ url_for('main.edit_application', id=application.id) }}">
            <div class="mb-3">
                <label for="name" class="form-label">应用名称</label>
                <input type="text" class="form-control" id="name" name="name" value="{{ application.name }}" required>
                <div class="form-text">应用的唯一标识名称</div>
            </div>

            <div class="mb-3">
                <label for="description" class="form-label">应用描述</label>
                <textarea class="form-control" id="description" name="description" rows="3">{{ application.description }}</textarea>
                <div class="form-text">应用的简要描述</div>
            </div>

            <div class="mb-3">
                <label for="api_key" class="form-label">API密钥</label>
                <div class="input-group">
                    <input type="text" class="form-control" id="api_key" value="{{ application.api_key }}" readonly>
                    <button class="btn btn-outline-secondary copy-btn" type="button" 
                            data-clipboard-text="{{ application.api_key }}">
                        <i class="bi bi-clipboard"></i> 复制
                    </button>
                </div>
                <div class="form-text">应用的API密钥，用于访问模型API</div>
            </div>

            <div class="d-flex justify-content-between">
                <a href="{{ url_for('main.list_applications') }}" class="btn btn-secondary">返回</a>
                <button type="submit" class="btn btn-primary">更新</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/clipboard@2.0.11/dist/clipboard.min.js"></script>
<script>
    // 初始化剪贴板功能
    var clipboard = new ClipboardJS('.copy-btn');

    clipboard.on('success', function(e) {
        // 显示复制成功提示
        var button = e.trigger;
        var originalHtml = button.innerHTML;
        button.innerHTML = '<i class="bi bi-check"></i> 已复制';
        button.classList.add('btn-success');
        button.classList.remove('btn-outline-secondary');

        setTimeout(function() {
            button.innerHTML = originalHtml;
            button.classList.remove('btn-success');
            button.classList.add('btn-outline-secondary');
        }, 2000);

        e.clearSelection();
    });
</script>
{% endblock %}
