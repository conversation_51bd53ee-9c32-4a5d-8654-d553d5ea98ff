{% extends "base.html" %}

{% block title %}编辑平台 - 大语言模型管理系统{% endblock %}

{% block header %}编辑平台{% endblock %}

{% block content %}
<div class="card">
    <div class="card-body">
        <form method="post" action="{{ url_for('main.edit_platform', id=platform.id) }}">
            <div class="mb-3">
                <label for="name" class="form-label">平台名称</label>
                <input type="text" class="form-control" id="name" name="name" value="{{ platform.name }}" required>
                <div class="form-text">平台的唯一标识名称，如 OpenAI、Azure 等</div>
            </div>

            <div class="mb-3">
                <label for="base_url" class="form-label">API地址</label>
                <input type="text" class="form-control" id="base_url" name="base_url" value="{{ platform.base_url }}" required>
                <div class="form-text">平台的API基础URL，如 https://api.openai.com/v1</div>
            </div>

            <div class="mb-3">
                <label for="api_key" class="form-label">API密钥</label>
                <input type="text" class="form-control" id="api_key" name="api_key" value="{{ platform.api_key }}" required>
                <div class="form-text">用于访问平台API的密钥</div>
            </div>

            <div class="d-flex justify-content-between">
                <a href="{{ url_for('main.list_platforms') }}" class="btn btn-secondary">返回</a>
                <button type="submit" class="btn btn-primary">更新</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
