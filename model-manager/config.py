import os
from datetime import timedelta

class Config:
    """基础配置类"""
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_size': 10,
        'pool_timeout': 20,
        'pool_recycle': -1,
        'pool_pre_ping': True
    }
    
    # 会话配置
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    SESSION_COOKIE_SECURE = False  # 默认为False，生产环境会覆盖
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    
    # API配置
    API_ENABLED = os.environ.get('API_ENABLED', 'false').lower() == 'true'
    API_VERSION = 'v1'
    
    # 安全配置
    ADMIN_PASSWORD = os.environ.get('ADMIN_PASSWORD', 'admin123')
    MAX_LOGIN_ATTEMPTS = int(os.environ.get('MAX_LOGIN_ATTEMPTS', '5'))
    LOGIN_ATTEMPT_TIMEOUT = int(os.environ.get('LOGIN_ATTEMPT_TIMEOUT', '300'))  # 5分钟
    
    # 分页配置
    ITEMS_PER_PAGE = int(os.environ.get('ITEMS_PER_PAGE', '20'))

    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')

    # 数据库连接池配置
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_size': int(os.environ.get('DB_POOL_SIZE', '10')),
        'pool_timeout': int(os.environ.get('DB_POOL_TIMEOUT', '20')),
        'pool_recycle': int(os.environ.get('DB_POOL_RECYCLE', '-1')),
        'pool_pre_ping': os.environ.get('DB_POOL_PRE_PING', 'true').lower() == 'true'
    }

    # 速率限制配置
    RATE_LIMIT_ENABLED = os.environ.get('RATE_LIMIT_ENABLED', 'true').lower() == 'true'
    RATE_LIMIT_DEFAULT = int(os.environ.get('RATE_LIMIT_DEFAULT', '100'))
    RATE_LIMIT_WINDOW = int(os.environ.get('RATE_LIMIT_WINDOW', '3600'))

    # 文件上传配置
    MAX_CONTENT_LENGTH = int(os.environ.get('MAX_CONTENT_LENGTH', '16777216'))  # 16MB
    UPLOAD_FOLDER = os.environ.get('UPLOAD_FOLDER', 'uploads')

    # 缓存配置
    CACHE_TYPE = os.environ.get('CACHE_TYPE', 'simple')
    CACHE_DEFAULT_TIMEOUT = int(os.environ.get('CACHE_DEFAULT_TIMEOUT', '300'))

    # 监控配置
    MONITORING_ENABLED = os.environ.get('MONITORING_ENABLED', 'false').lower() == 'true'
    METRICS_ENDPOINT = os.environ.get('METRICS_ENDPOINT', '/metrics')

    # 备份配置
    BACKUP_ENABLED = os.environ.get('BACKUP_ENABLED', 'false').lower() == 'true'
    BACKUP_INTERVAL = int(os.environ.get('BACKUP_INTERVAL', '86400'))  # 24小时
    BACKUP_RETENTION = int(os.environ.get('BACKUP_RETENTION', '7'))    # 保留7天

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'mysql+pymysql://root:rootpassword@localhost:3306/model_registry'
    SESSION_COOKIE_SECURE = False
    LOG_LEVEL = 'DEBUG'
    WTF_CSRF_ENABLED = True

    # 开发环境特定配置
    SQLALCHEMY_ECHO = os.environ.get('SQLALCHEMY_ECHO', 'false').lower() == 'true'
    TESTING = False

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'mysql+pymysql://root:rootpassword@db:3306/model_registry'

    # 生产环境安全设置
    SESSION_COOKIE_SECURE = False  # 临时设置为False，因为我们使用HTTP
    WTF_CSRF_ENABLED = True
    LOG_LEVEL = 'WARNING'

    # 生产环境特定配置
    SQLALCHEMY_ECHO = False
    TESTING = False

    # 强制HTTPS
    PREFERRED_URL_SCHEME = 'https'

    # 更严格的会话配置
    PERMANENT_SESSION_LIFETIME = timedelta(hours=8)  # 8小时过期

class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    WTF_CSRF_ENABLED = False
    SESSION_COOKIE_SECURE = False

# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

def get_config():
    """获取当前环境配置"""
    env = os.environ.get('FLASK_ENV', 'development')
    return config.get(env, config['default'])
