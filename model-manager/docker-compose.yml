version: '3.8'

services:
  model-manager:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: model-manager
    restart: unless-stopped
    ports:
      - "5001:5001"
    env_file:
      - .env
    environment:
      FLASK_ENV: production
      PYTHONUNBUFFERED: 1
      SERVICE_TYPE: model-manager
    volumes:
      - model_logs:/app/logs
      - model_uploads:/app/uploads
    networks:
      - shared-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5001/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s


networks:
  shared-network:
    name: llm-system-network
    external: true

volumes:
  model_logs:
    driver: local
  model_uploads:
    driver: local
