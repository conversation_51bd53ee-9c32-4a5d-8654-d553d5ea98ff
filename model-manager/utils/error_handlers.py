from flask import render_template, jsonify, request, current_app
from werkzeug.exceptions import HTTPException
import logging
import traceback

logger = logging.getLogger(__name__)

def register_error_handlers(app):
    """注册全局错误处理器"""
    
    @app.errorhandler(400)
    def bad_request(error):
        """400 错误处理"""
        logger.warning(f"Bad request: {request.url} - {str(error)}")
        
        if request.is_json or request.path.startswith('/api/'):
            return jsonify({
                'error': 'Bad Request',
                'message': '请求格式错误',
                'status_code': 400
            }), 400
        
        return render_template('errors/400.html'), 400
    
    @app.errorhandler(401)
    def unauthorized(error):
        """401 错误处理"""
        logger.warning(f"Unauthorized access: {request.url} - IP: {request.remote_addr}")
        
        if request.is_json or request.path.startswith('/api/'):
            return jsonify({
                'error': 'Unauthorized',
                'message': '未授权访问',
                'status_code': 401
            }), 401
        
        return render_template('errors/401.html'), 401
    
    @app.errorhandler(403)
    def forbidden(error):
        """403 错误处理"""
        logger.warning(f"Forbidden access: {request.url} - IP: {request.remote_addr}")
        
        if request.is_json or request.path.startswith('/api/'):
            return jsonify({
                'error': 'Forbidden',
                'message': '禁止访问',
                'status_code': 403
            }), 403
        
        return render_template('errors/403.html'), 403
    
    @app.errorhandler(404)
    def not_found(error):
        """404 错误处理"""
        logger.info(f"Page not found: {request.url} - IP: {request.remote_addr}")
        
        if request.is_json or request.path.startswith('/api/'):
            return jsonify({
                'error': 'Not Found',
                'message': '资源不存在',
                'status_code': 404
            }), 404
        
        return render_template('errors/404.html'), 404
    
    @app.errorhandler(405)
    def method_not_allowed(error):
        """405 错误处理"""
        logger.warning(f"Method not allowed: {request.method} {request.url} - IP: {request.remote_addr}")
        
        if request.is_json or request.path.startswith('/api/'):
            return jsonify({
                'error': 'Method Not Allowed',
                'message': '请求方法不允许',
                'status_code': 405
            }), 405
        
        return render_template('errors/405.html'), 405
    
    @app.errorhandler(429)
    def rate_limit_exceeded(error):
        """429 错误处理"""
        logger.warning(f"Rate limit exceeded: {request.url} - IP: {request.remote_addr}")
        
        if request.is_json or request.path.startswith('/api/'):
            return jsonify({
                'error': 'Too Many Requests',
                'message': '请求过于频繁，请稍后再试',
                'status_code': 429
            }), 429
        
        return render_template('errors/429.html'), 429
    
    @app.errorhandler(500)
    def internal_error(error):
        """500 错误处理"""
        # 记录详细的错误信息
        error_id = f"ERR_{hash(str(error))}"
        logger.error(f"Internal server error [{error_id}]: {str(error)}")
        logger.error(f"Traceback [{error_id}]: {traceback.format_exc()}")
        
        # 回滚数据库事务
        from models import db
        db.session.rollback()
        
        if request.is_json or request.path.startswith('/api/'):
            return jsonify({
                'error': 'Internal Server Error',
                'message': '服务器内部错误',
                'error_id': error_id,
                'status_code': 500
            }), 500
        
        return render_template('errors/500.html', error_id=error_id), 500
    
    @app.errorhandler(Exception)
    def handle_exception(error):
        """处理未捕获的异常"""
        # 如果是HTTP异常，让其他处理器处理
        if isinstance(error, HTTPException):
            return error
        
        # 记录未捕获的异常
        error_id = f"UNCAUGHT_{hash(str(error))}"
        logger.critical(f"Uncaught exception [{error_id}]: {str(error)}")
        logger.critical(f"Traceback [{error_id}]: {traceback.format_exc()}")
        
        # 回滚数据库事务
        from models import db
        db.session.rollback()
        
        if request.is_json or request.path.startswith('/api/'):
            return jsonify({
                'error': 'Internal Server Error',
                'message': '服务器遇到未知错误',
                'error_id': error_id,
                'status_code': 500
            }), 500
        
        return render_template('errors/500.html', error_id=error_id), 500

class ApplicationError(Exception):
    """应用自定义异常基类"""
    def __init__(self, message, status_code=500, payload=None):
        super().__init__()
        self.message = message
        self.status_code = status_code
        self.payload = payload

class ValidationError(ApplicationError):
    """验证错误"""
    def __init__(self, message, payload=None):
        super().__init__(message, 400, payload)

class AuthenticationError(ApplicationError):
    """认证错误"""
    def __init__(self, message, payload=None):
        super().__init__(message, 401, payload)

class AuthorizationError(ApplicationError):
    """授权错误"""
    def __init__(self, message, payload=None):
        super().__init__(message, 403, payload)

class NotFoundError(ApplicationError):
    """资源不存在错误"""
    def __init__(self, message, payload=None):
        super().__init__(message, 404, payload)

class ConflictError(ApplicationError):
    """冲突错误"""
    def __init__(self, message, payload=None):
        super().__init__(message, 409, payload)

class RateLimitError(ApplicationError):
    """速率限制错误"""
    def __init__(self, message, payload=None):
        super().__init__(message, 429, payload)

def handle_application_error(app):
    """注册应用自定义异常处理器"""
    
    @app.errorhandler(ApplicationError)
    def handle_app_error(error):
        """处理应用自定义异常"""
        logger.warning(f"Application error: {error.message} - Status: {error.status_code}")
        
        response_data = {
            'error': error.__class__.__name__,
            'message': error.message,
            'status_code': error.status_code
        }
        
        if error.payload:
            response_data['details'] = error.payload
        
        if request.is_json or request.path.startswith('/api/'):
            return jsonify(response_data), error.status_code
        
        # 对于Web请求，显示友好的错误页面
        template_map = {
            400: 'errors/400.html',
            401: 'errors/401.html',
            403: 'errors/403.html',
            404: 'errors/404.html',
            409: 'errors/409.html',
            429: 'errors/429.html'
        }
        
        template = template_map.get(error.status_code, 'errors/500.html')
        return render_template(template, error=error), error.status_code

def safe_execute(func, *args, **kwargs):
    """安全执行函数，捕获并记录异常"""
    try:
        return func(*args, **kwargs)
    except Exception as e:
        logger.error(f"Error executing {func.__name__}: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise

def log_request_info():
    """记录请求信息"""
    logger.info(f"Request: {request.method} {request.url} - IP: {request.remote_addr} - UA: {request.user_agent}")

def log_response_info(response):
    """记录响应信息"""
    logger.info(f"Response: {response.status_code} - Size: {response.content_length}")
    return response
