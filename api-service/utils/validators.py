from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, DecimalField, BooleanField, SelectField, PasswordField
from wtforms.validators import DataRequired, Length, URL, NumberRange, Optional, Regexp
from wtforms import ValidationError
from models import Platform, AIModel, Application

class PlatformForm(FlaskForm):
    """平台表单验证"""
    name = StringField('平台名称', validators=[
        DataRequired(message='平台名称不能为空'),
        Length(min=1, max=50, message='平台名称长度必须在1-50字符之间'),
        Regexp(r'^[a-zA-Z0-9_\-\u4e00-\u9fff\s]+$', message='平台名称只能包含字母、数字、下划线、连字符和中文')
    ])
    
    base_url = StringField('基础URL', validators=[
        DataRequired(message='基础URL不能为空'),
        URL(message='请输入有效的URL'),
        Length(max=255, message='URL长度不能超过255字符')
    ])
    
    api_key = StringField('API密钥', validators=[
        DataRequired(message='API密钥不能为空'),
        Length(min=10, max=255, message='API密钥长度必须在10-255字符之间'),
        Regexp(r'^[a-zA-Z0-9_\-]+$', message='API密钥只能包含字母、数字、下划线和连字符')
    ])
    
    def validate_name(self, field):
        """验证平台名称唯一性"""
        platform = Platform.query.filter_by(name=field.data).first()
        if platform:
            raise ValidationError('平台名称已存在')

class ModelForm(FlaskForm):
    """模型表单验证"""
    display_name = StringField('显示名称', validators=[
        DataRequired(message='显示名称不能为空'),
        Length(min=1, max=50, message='显示名称长度必须在1-50字符之间'),
        Regexp(r'^[a-zA-Z0-9_\-\u4e00-\u9fff\s\.]+$', message='显示名称格式不正确')
    ])
    
    internal_name = StringField('内部名称', validators=[
        DataRequired(message='内部名称不能为空'),
        Length(min=1, max=100, message='内部名称长度必须在1-100字符之间'),
        Regexp(r'^[a-zA-Z0-9_\-\./:]+$', message='内部名称只能包含字母、数字、下划线、连字符、点号、冒号和斜杠')
    ])
    
    api_endpoint = StringField('API端点', validators=[
        Optional(),
        Length(max=255, message='API端点长度不能超过255字符')
    ])
    
    platform_id = SelectField('所属平台', coerce=int, validators=[
        DataRequired(message='请选择所属平台')
    ])
    
    input_token_price = DecimalField('输入Token价格', validators=[
        Optional(),
        NumberRange(min=0, max=999.999999, message='价格必须在0-999.999999之间')
    ], places=6)
    
    output_token_price = DecimalField('输出Token价格', validators=[
        Optional(),
        NumberRange(min=0, max=999.999999, message='价格必须在0-999.999999之间')
    ], places=6)
    
    input_picture_price = DecimalField('输入图片价格', validators=[
        Optional(),
        NumberRange(min=0, max=999.999999, message='价格必须在0-999.999999之间')
    ], places=6)
    
    is_visible_model = BooleanField('是否可见')
    free = BooleanField('是否免费')
    high_price = BooleanField('是否高价')
    
    def validate_display_name(self, field):
        """验证显示名称唯一性"""
        model = AIModel.query.filter_by(display_name=field.data).first()
        if model:
            raise ValidationError('显示名称已存在')

class ApplicationForm(FlaskForm):
    """应用表单验证"""
    name = StringField('应用名称', validators=[
        DataRequired(message='应用名称不能为空'),
        Length(min=1, max=100, message='应用名称长度必须在1-100字符之间'),
        Regexp(r'^[a-zA-Z0-9_\-\u4e00-\u9fff\s]+$', message='应用名称只能包含字母、数字、下划线、连字符和中文')
    ])
    
    description = TextAreaField('应用描述', validators=[
        Optional(),
        Length(max=1000, message='描述长度不能超过1000字符')
    ])
    
    def validate_name(self, field):
        """验证应用名称唯一性"""
        application = Application.query.filter_by(name=field.data).first()
        if application:
            raise ValidationError('应用名称已存在')

class LoginForm(FlaskForm):
    """登录表单验证"""
    password = PasswordField('密码', validators=[
        DataRequired(message='密码不能为空'),
        Length(min=1, max=100, message='密码长度不能超过100字符')
    ])

class AppModelForm(FlaskForm):
    """应用模型关联表单验证"""
    application_id = SelectField('应用', coerce=int, validators=[
        DataRequired(message='请选择应用')
    ])
    
    model_id = SelectField('模型', coerce=int, validators=[
        DataRequired(message='请选择模型')
    ])
    
    is_default = BooleanField('设为默认')

def validate_json_input(data, required_fields=None):
    """验证JSON输入"""
    if not isinstance(data, dict):
        return False, "输入必须是JSON对象"
    
    if required_fields:
        for field in required_fields:
            if field not in data:
                return False, f"缺少必需字段: {field}"
    
    return True, "验证通过"

def sanitize_string(input_str):
    """清理字符串输入"""
    if not input_str:
        return ""
    
    # 移除潜在的危险字符
    dangerous_chars = ['<', '>', '"', "'", '&', '\x00']
    for char in dangerous_chars:
        input_str = input_str.replace(char, '')
    
    return input_str.strip()
