version: '3.8'

services:
  api-service:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: api-service
    restart: unless-stopped
    ports:
      - "5002:5002"
    env_file:
      - .env
    environment:
      FLASK_ENV: production
      PYTHONUNBUFFERED: 1
      SERVICE_TYPE: api-service
    volumes:
      - api_logs:/app/logs
      - api_uploads:/app/uploads
    networks:
      - shared-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5002/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  shared-network:
    name: llm-system-network
    external: true

volumes:
  api_logs:
    driver: local
  api_uploads:
    driver: local
