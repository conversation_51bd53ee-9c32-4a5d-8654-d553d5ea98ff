# app.py - 纯API服务
from flask import Flask, jsonify
from models import db
from config import get_config
from utils.logging_config import setup_logging
from utils.error_handlers import register_error_handlers, handle_application_error
from blueprints.api import api_bp
import os
from dotenv import load_dotenv

# 加载环境变量
def load_environment():
    """根据FLASK_ENV加载相应的环境变量文件"""
    flask_env = os.environ.get('FLASK_ENV', 'development')

    # 尝试加载特定环境的配置文件
    env_file = f'.env.{flask_env}'
    if os.path.exists(env_file):
        load_dotenv(env_file)
        print(f"Loaded environment from {env_file}")
    else:
        # 回退到默认的.env文件
        if os.path.exists('.env'):
            load_dotenv('.env')
            print("Loaded environment from .env")
        else:
            print("No .env file found, using system environment variables")

# 在应用创建前加载环境变量
load_environment()

def create_app(config_name=None):
    """纯API应用工厂函数"""
    app = Flask(__name__)

    # 加载配置
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')

    config_class = get_config()
    app.config.from_object(config_class)

    # 初始化扩展
    db.init_app(app)

    # 设置日志
    setup_logging(app)

    # 注册错误处理器
    register_error_handlers(app)
    handle_application_error(app)

    # 只注册API蓝图
    app.register_blueprint(api_bp)

    # 根路径重定向到API文档
    @app.route('/')
    def index():
        return jsonify({
            'service': 'LLM API Service',
            'version': app.config.get('API_VERSION', 'v1'),
            'status': 'running',
            'endpoints': {
                'health': '/api/v1/health',
                'platforms': '/api/v1/platforms',
                'models': '/api/v1/models',
                'applications': '/api/v1/applications',
                'stats': '/api/v1/stats'
            },
            'documentation': 'This is a pure API service for LLM management'
        })

    # 创建数据库表
    with app.app_context():
        db.create_all()

    return app

# 创建应用实例
app = create_app()

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    app.run(debug=True, host='0.0.0.0', port=5002)
