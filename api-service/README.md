# LLM API Service (纯API服务)

这是大语言模型管理系统的纯API服务组件，运行在5002端口。

## 功能特性

- **纯API服务**：只提供RESTful API接口，不包含Web界面
- **平台管理**：查询AI平台信息
- **模型管理**：查询AI模型信息
- **应用管理**：查询应用配置信息
- **统计信息**：获取系统统计数据
- **健康检查**：服务状态监控

## API端点

### 基础信息
- `GET /` - 服务信息和API文档
- `GET /api/v1/health` - 健康检查

### 平台相关
- `GET /api/v1/platforms` - 获取所有平台列表
- `GET /api/v1/platforms/{id}` - 获取特定平台详情

### 模型相关
- `GET /api/v1/models` - 获取所有模型列表
- `GET /api/v1/models/{id}` - 获取特定模型详情
- 支持查询参数：
  - `platform_id` - 按平台过滤
  - `visible_only=true` - 只显示可见模型
  - `free_only=true` - 只显示免费模型

### 应用相关
- `GET /api/v1/applications` - 获取应用列表（需要API密钥）
- `GET /api/v1/app/{api_key}/models` - 根据API密钥获取应用关联的模型

### 统计信息
- `GET /api/v1/stats` - 获取系统统计信息

## 使用示例

```bash
# 获取服务信息
curl http://localhost:5002/

# 健康检查
curl http://localhost:5002/api/v1/health

# 获取所有平台
curl http://localhost:5002/api/v1/platforms

# 获取所有模型
curl http://localhost:5002/api/v1/models

# 获取统计信息
curl http://localhost:5002/api/v1/stats

# 获取特定平台的模型
curl "http://localhost:5002/api/v1/models?platform_id=1"

# 只获取可见的免费模型
curl "http://localhost:5002/api/v1/models?visible_only=true&free_only=true"
```

## 响应格式

所有API响应都采用JSON格式：

```json
{
  "success": true,
  "data": [...],
  "count": 3
}
```

错误响应：
```json
{
  "success": false,
  "error": "错误信息"
}
```

## 配置

- 端口：5002
- 数据库：连接到模型数据库（models-db）
- API默认启用
- 支持速率限制和安全验证

## 与其他服务的区别

- **5001端口（模型管理服务）**：提供Web界面和管理功能
- **5002端口（API服务）**：纯API接口，无Web界面，专注于数据查询
- 用户聊天界面
- 对话记录管理
- Token使用统计和费用计算
- 用户余额管理

## 服务信息

- **端口**: 5002
- **主数据库**: records-db (聊天记录数据库)
- **只读数据库**: models-db (模型数据库)
- **服务类型**: api-service

## 主要功能模块

### API调用
- 统一的AI模型调用接口
- 支持多平台模型切换
- 自动token计费和余额扣除

### 聊天功能
- Web聊天界面
- 对话历史管理
- 实时消息流式传输

### 用户管理
- 用户注册和认证
- API密钥管理
- 余额和使用统计

### 数据记录
- 完整的对话记录
- Token使用统计
- 费用计算和记录

## 启动服务

### 前置条件
确保依赖服务已启动：
```bash
# 启动聊天记录数据库
cd app/records-database
docker-compose up -d

# 启动模型数据库
cd app/models-database
docker-compose up -d

# 启动模型管理服务
cd app/model-manager
docker-compose up -d
```

### 启动API服务
```bash
# 在当前目录下
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

## 访问服务

- **Web界面**: http://localhost:5002
- **API接口**: http://localhost:5002/api/v1/
- **聊天界面**: http://localhost:5002/chat
- **健康检查**: http://localhost:5002/api/v1/health

## API使用示例

```bash
# 获取可用模型列表
curl -X GET "http://localhost:5002/api/v1/models" \
     -H "Authorization: Bearer YOUR_API_KEY"

# 发送聊天请求
curl -X POST "http://localhost:5002/api/v1/chat/completions" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_API_KEY" \
     -d '{
       "model": "gpt-3.5-turbo",
       "messages": [
         {"role": "user", "content": "Hello!"}
       ]
     }'
```

## 停止服务

```bash
docker-compose down
```

## 环境变量配置

主要配置项在 `.env` 文件中：

- `DATABASE_URL`: 聊天记录数据库连接
- `MODELS_DATABASE_URL`: 模型数据库连接（只读）
- `MODELS_SERVICE_URL`: 模型管理服务地址
- `ADMIN_PASSWORD`: 管理员密码

## 依赖服务

1. **records-db**: 聊天记录数据库（必须）
2. **models-db**: 模型数据库（只读访问）
3. **model-manager**: 模型管理服务（可选）

## 注意事项

1. 确保所有依赖服务已启动并可访问
2. 服务需要同时连接两个数据库
3. 生产环境请修改默认密码和密钥
4. 监控API调用频率和用户余额
5. 定期备份聊天记录数据
