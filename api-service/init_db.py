# init_db.py
import logging
import os
import json
from decimal import Decimal
from pathlib import Path
from db_operator import create_user

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_new_billing_database():
    """
    创建全新的包含Token计费功能的数据库
    数据库名称: chat_system.db
    整合了之前数据库中的平台信息
    """
    # 检查并删除现有数据库文件(如果存在)
    db_path = 'database/chat_system.db'
    if os.path.exists(db_path):
        try:
            os.remove(db_path)
            logger.info(f"已删除现有数据库文件: {db_path}")
        except Exception as e:
            logger.error(f"无法删除现有数据库文件: {e}")
            return False

    try:
        # 导入数据库模型
        from db_models import (
            Base, Platform, AIModel, User, Conversation, Message, Transaction,
            engine, SessionLocal
        )

        # 创建所有表
        Base.metadata.create_all(engine)
        logger.info("已创建所有数据库表")

        # 创建会话
        session = SessionLocal()
        try:
            # 添加平台数据 - 整合之前的平台信息
            platforms_data = [
                {
                    "name": "dashscope",
                    "base_url": r"https://dashscope.aliyuncs.com/compatible-mode/v1",
                    "api_key": "sk-d4a1d2d141b049129a9a0ba97165c6df"
                },
                {
                    "name": "deepseek",
                    "base_url": r"https://api.deepseek.com",
                    "api_key": "***********************************"
                },
                {
                    "name": "GLM",
                    "base_url": r"https://open.bigmodel.cn/api/paas/v4/",
                    "api_key": "5f75aff7cf3529f24bc174f14e505c60.CjP9A7jx4K7pIxdq"
                },
                {
                    "name": "spark",
                    "base_url": r"https://spark-api-open.xf-yun.com/v1",
                    "api_key": "fb4d51d6a4e8802f6c0febf24ac75d58:YmNhZWY2YmI0MDU0ZTU2YjRjMTdhMGU1"
                },
                {
                    "name": "OpenRouter",
                    "base_url": r"https://openrouter.ai/api/v1",
                    "api_key": "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"
                },
                {
                    "name": "deepbricks",
                    "base_url": r"https://api.deepbricks.ai/v1/",
                    "api_key": "sk-pmLx4UQDPWYhQYJqc8MQgbQ6pkmkRoQ6YxTumIy5nQWjSnIR"
                }
            ]

            platform_objects = {}
            for platform_info in platforms_data:
                platform = Platform(
                    name=platform_info["name"],
                    base_url=platform_info["base_url"],
                    api_key=platform_info["api_key"]
                )
                session.add(platform)
                session.flush()  # 刷新以获取ID
                platform_objects[platform.name] = platform

            logger.info("已添加平台数据")

            # 尝试加载之前的模型配置
            try:
                models_path = Path('database/models.json')
                if models_path.exists():
                    with open(models_path, 'r') as f:
                        models_data = json.load(f)

                    # 从配置文件加载模型
                    models_list = []
                    for model_name, config in models_data.items():
                        platform_name = config['platform']
                        if platform_name in platform_objects:
                            model = AIModel(
                                display_name=model_name,
                                internal_name=config['model'],
                                api_endpoint=config.get('url', ''),
                                platform_id=platform_objects[platform_name].id,
                                input_token_price=Decimal(str(config.get('input-price', 0.0))) / Decimal('1000'),
                                output_token_price=Decimal(str(config.get('output-price', 0.0))) / Decimal('1000'),
                                input_picture_price=Decimal(str(config.get('picture-price', 0.0))) / Decimal('1000'),
                                is_visible_model = config.get('visible-model', False),
                                free = config.get('free', False),
                                high_price = config.get('high-price', False)
                            )
                            models_list.append(model)

                    session.add_all(models_list)
                    session.flush()
                    logger.info(f"已从配置文件加载{len(models_list)}个模型")
            except Exception as e:
                logger.warning(f"加载模型配置失败: {e}, 将使用默认模型配置")

            # 提交当前所有更改，确保平台和模型已正确添加
            session.commit()

            # 创建管理员用户 - 使用新的会话以避免锁定问题
            session.close()

            # 创建管理员用户
            admin_user, admin_api_key = create_user(
                permission=9,  # 管理员权限
                current_model_id=1,
                initial_balance=5.0
            )

            if admin_user is None or admin_api_key is None:
                raise Exception("创建管理员用户失败")

            logger.info(f"管理员用户已创建，API密钥: {admin_api_key}")

            logger.info(f"数据库 {db_path} 创建成功！")
            return True

        except Exception as e:
            session.rollback()
            logger.error(f"数据库初始化过程中出错: {e}")
            raise
        finally:
            # 确保会话被关闭
            if session:
                session.close()

    except Exception as e:
        logger.error(f"创建数据库失败: {e}")
        # 如果出错，尝试删除可能部分创建的数据库文件
        if os.path.exists(db_path):
            try:
                os.remove(db_path)
                logger.info(f"已删除部分创建的数据库文件: {db_path}")
            except Exception as del_err:
                logger.error(f"无法删除部分创建的数据库文件: {del_err}")
        return False


if __name__ == "__main__":
    print("正在创建新的带计费功能的数据库...")
    success = create_new_billing_database()
    if success:
        print("数据库创建成功！")
    else:
        print("数据库创建失败，请查看日志了解详情。")
