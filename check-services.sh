#!/bin/bash

# 检查所有服务状态的脚本

echo "=== 服务状态检查 ==="
echo ""

echo "1. 模型数据库服务:"
(cd models-database && docker compose ps)
echo ""

echo "2. 聊天记录数据库服务:"
(cd records-database && docker compose ps)
echo ""

echo "3. 模型管理服务:"
(cd model-manager && docker compose ps)
echo ""

echo "4. API调用服务:"
(cd api-service && docker compose ps)
echo ""

echo "=== 健康检查 ==="
echo "检查模型管理服务..."
curl -f http://localhost:5001/api/v1/health 2>/dev/null && echo "✓ 模型管理服务正常" || echo "✗ 模型管理服务异常"

echo "检查API服务..."
curl -f http://localhost:5002/api/v1/health 2>/dev/null && echo "✓ API服务正常" || echo "✗ API服务异常"

echo ""
echo "=== 端口使用情况 ==="
echo "3306: 模型数据库"
echo "3307: 聊天记录数据库"
echo "5001: 模型管理服务"
echo "5002: API调用服务"
